<?php
if(!$this->session->userdata('id')) {
	redirect(base_url().'admin');
}
?>
<section class="content-header">
	<div class="content-header-left">
		<h1>Edit Language</h1>
	</div>
	<div class="content-header-right">
		<a href="<?php echo base_url(); ?>admin/lang" class="btn btn-primary btn-sm">View All</a>
	</div>
</section>


<section class="content">

	<div class="row">
		<div class="col-md-12">

			<?php
			if($this->session->flashdata('error')) {
				?>
				<div class="callout callout-danger">
					<p><?php echo $this->session->flashdata('error'); ?></p>
				</div>
				<?php
			}
			if($this->session->flashdata('success')) {
				?>
				<div class="callout callout-success">
					<p><?php echo $this->session->flashdata('success'); ?></p>
				</div>
				<?php
			}
			?>

			<?php echo form_open(base_url().'admin/lang/edit/'.$lang['lang_id'],array('class' => 'form-horizontal')); ?>
				<div class="box box-info">
					<div class="box-body">
						<div class="form-group">
							<label for="" class="col-sm-2 control-label">Language Name *</label>
							<div class="col-sm-4">
								<input type="text" class="form-control" name="lang_name" value="<?php echo $lang['lang_name']; ?>">
							</div>
						</div>
						<div class="form-group">
							<label for="" class="col-sm-2 control-label">Language Short Name *</label>
							<div class="col-sm-4">
								<input type="text" class="form-control" name="lang_short_name" value="<?php echo $lang['lang_short_name']; ?>">
							</div>
						</div>
						<div class="form-group">
							<label for="" class="col-sm-2 control-label">Layout Direction *</label>
							<div class="col-sm-4">
								<select name="layout_direction" class="form-control">
									<option value="Left" <?php if($lang['layout_direction'] == 'Left') {echo 'selected';} ?>>Left</option>
									<option value="Right" <?php if($lang['layout_direction'] == 'Right') {echo 'selected';} ?>>Right</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label for="" class="col-sm-2 control-label">Language Default?</label>
							<div class="col-sm-4" style="padding-top: 5px;">
								<input type="hidden" name="lang_default" value="0">
								<input type="checkbox" name="lang_default" value="1" <?php if($lang['lang_default'] == 'Yes') {echo 'checked';} ?>>
							</div>
						</div>
						<div class="form-group">
							<label for="" class="col-sm-2 control-label"></label>
							<div class="col-sm-6">
								<button type="submit" class="btn btn-success pull-left" name="form1">Submit</button>
							</div>
						</div>
					</div>
				</div>
			<?php echo form_close(); ?>
		</div>
	</div>
</section>